<!-- 现代化商品卡片 -->
<div class="product-card card-modern hover-scale glow-effect" data-aos="fade-up" data-aos-delay="100">
    <div class="relative overflow-hidden">
        <!-- 商品图片 -->
        <div class="relative group">
            <a href="<?php if($goods['in_stock'] > 0): ?> <?php echo e(url("/buy/{$goods['id']}"), false); ?> <?php else: ?> javascript:void(0); <?php endif; ?>"
               <?php if($goods['in_stock'] <= 0): ?>
                  onclick="showNotification('商品库存不足, 请联系客服补货.', 'warning');"
               <?php endif; ?>
               class="block">
                <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden">
                    <img src="<?php echo e(picture_url($goods['picture']), false); ?>" 
                         alt="<?php echo e($goods['gd_name'], false); ?>"
                         class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110">
                </div>
                
                <!-- 悬停遮罩 -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl flex items-end justify-center pb-4">
                    <span class="text-white font-semibold">
                        <?php if($goods['in_stock'] > 0): ?>
                            <i class="fas fa-shopping-cart mr-2"></i>立即购买
                        <?php else: ?>
                            <i class="fas fa-exclamation-triangle mr-2"></i>暂时缺货
                        <?php endif; ?>
                    </span>
                </div>
            </a>
        </div>

        <!-- 状态标签 -->
        <div class="absolute top-3 left-3 flex flex-col gap-2 z-10">
            <!-- 发货类型标签 -->
            <?php if($goods['type'] == \App\Models\Goods::AUTOMATIC_DELIVERY): ?>
                <span class="tag bg-green-500 text-white">
                    <i class="fas fa-bolt mr-1"></i>自动发货
                </span>
            <?php else: ?>
                <span class="tag bg-blue-500 text-white">
                    <i class="fas fa-user mr-1"></i>人工发货
                </span>
            <?php endif; ?>

            <!-- 库存状态标签 -->
            <?php if($goods['in_stock'] <= 0): ?>
                <span class="tag bg-red-500 text-white">
                    <i class="fas fa-times mr-1"></i>缺货
                </span>
            <?php elseif($goods['in_stock'] <= 5): ?>
                <span class="tag bg-orange-500 text-white">
                    <i class="fas fa-exclamation mr-1"></i>库存紧张
                </span>
            <?php endif; ?>
        </div>

        <!-- 热销标签 -->
        <?php if(isset($goods['is_hot']) && $goods['is_hot']): ?>
            <div class="absolute top-3 right-3 z-10">
                <span class="tag bg-red-500 text-white animate-pulse">
                    <i class="fas fa-fire mr-1"></i>热销
                </span>
            </div>
        <?php endif; ?>
    </div>

    <!-- 商品信息 -->
    <div class="p-6">
        <!-- 商品标题 -->
        <h3 class="text-lg font-bold text-white mb-2 line-clamp-2 hover:text-blue-400 transition-colors">
            <a href="<?php if($goods['in_stock'] > 0): ?> <?php echo e(url("/buy/{$goods['id']}"), false); ?> <?php else: ?> javascript:void(0); <?php endif; ?>"
               <?php if($goods['in_stock'] <= 0): ?>
                  onclick="showNotification('商品库存不足, 请联系客服补货.', 'warning');"
               <?php endif; ?>>
                <?php echo e($goods['gd_name'], false); ?>

            </a>
        </h3>

        <!-- 商品描述 -->
        <?php if(isset($goods['gd_description']) && $goods['gd_description']): ?>
            <p class="text-gray-400 text-sm mb-3 line-clamp-2">
                <?php echo e($goods['gd_description'], false); ?>

            </p>
        <?php endif; ?>

        <!-- 价格和库存信息 -->
        <div class="flex items-center justify-between mb-4">
            <div class="text-2xl font-bold gradient-text">
                <?php echo e(__('dujiaoka.money_symbol'), false); ?><?php echo e(number_format($goods['price'] ?? 0, 2), false); ?>

            </div>
            <div class="text-sm text-gray-400">
                库存: <span class="text-white font-semibold"><?php echo e($goods['in_stock'], false); ?></span>
            </div>
        </div>

        <!-- 库存进度条 -->
        <?php
            $maxStock = 50;
            $inStock = (int) $goods['in_stock'];
            $percent = 0;
            $colorClass = 'bg-red-500';
            
            if($inStock > 0){
                $percent = ($inStock >= $maxStock) ? 100 : round(($inStock / $maxStock) * 100, 2);
                
                if($percent >= 70) {
                    $colorClass = 'bg-green-500';
                } elseif($percent >= 30) {
                    $colorClass = 'bg-yellow-500';
                } else {
                    $colorClass = 'bg-red-500';
                }
            }
        ?>
        
        <div class="mb-4">
            <div class="flex justify-between text-xs text-gray-400 mb-1">
                <span>库存状态</span>
                <span><?php echo e($percent, false); ?>%</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
                <div class="h-2 rounded-full <?php echo e($colorClass, false); ?> transition-all duration-300" 
                     style="width: <?php echo e($percent, false); ?>%"></div>
            </div>
        </div>

        <!-- 购买按钮 -->
        <?php if($goods['in_stock'] > 0): ?>
            <a href="<?php echo e(url("/buy/{$goods['id']}"), false); ?>" 
               class="btn-primary w-full text-center block">
                <i class="fas fa-shopping-cart mr-2"></i>
                立即购买
            </a>
        <?php else: ?>
            <button class="btn-secondary w-full opacity-50 cursor-not-allowed" disabled>
                <i class="fas fa-times mr-2"></i>
                暂时缺货
            </button>
        <?php endif; ?>

        <!-- 商品特性标签 -->
        <div class="flex flex-wrap gap-2 mt-3">
            <?php if($goods['type'] == \App\Models\Goods::AUTOMATIC_DELIVERY): ?>
                <span class="text-xs px-2 py-1 bg-green-500 bg-opacity-20 text-green-400 rounded-full">
                    即时到账
                </span>
            <?php endif; ?>
            
            <?php if(isset($goods['is_wholesale']) && $goods['is_wholesale']): ?>
                <span class="text-xs px-2 py-1 bg-blue-500 bg-opacity-20 text-blue-400 rounded-full">
                    支持批发
                </span>
            <?php endif; ?>
            
            <?php if($goods['in_stock'] > 20): ?>
                <span class="text-xs px-2 py-1 bg-purple-500 bg-opacity-20 text-purple-400 rounded-full">
                    库存充足
                </span>
            <?php endif; ?>
        </div>
    </div>

    <!-- 卡片底部装饰 -->
    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
</div>
<?php /**PATH /www/wwwroot/www.bgoom.top/resources/views/neon/layouts/_goods_modern.blade.php ENDPATH**/ ?>