<script src="/assets/neon/js/jquery-3.6.0.min.js"></script>
<script src="/assets/neon/js/chart.min.js"></script>
<script>
// 创建星空背景
function createStars() {
    const stars = document.getElementById('stars');
    if (stars) {
        const count = 200;

        for (let i = 0; i < count; i++) {
            const star = document.createElement('div');
            star.className = 'star';
            star.style.left = Math.random() * 100 + '%';
            star.style.top = Math.random() * 100 + '%';
            star.style.animationDelay = Math.random() * 3 + 's';
            stars.appendChild(star);
        }
    }
}

// 数字动画
function animateNumber(id, target) {
    const element = document.getElementById(id);
    if (element) {
        const start = 0;
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current).toLocaleString();
        }, 16);
    }
}

// 通知系统
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    createStars();
});
</script>
