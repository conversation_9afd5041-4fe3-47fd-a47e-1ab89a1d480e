
<!-- 星空背景 -->
<div class="stars" id="stars"></div>

<!-- 导航栏 -->
<nav class="bg-black bg-opacity-50 backdrop-blur-md fixed w-full z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
            <div class="flex items-center">
                <i class="fas fa-star text-3xl text-yellow-400 mr-3"></i>
                <span class="text-2xl font-bold">{{ dujiaoka_config_get('text_logo') }}</span>
            </div>
            <div class="flex space-x-4">
                <a href="/" class="hover:text-cyan-400 transition @if(\Illuminate\Support\Facades\Request::url() == url('/')) text-cyan-400 @endif">主页</a>
                <a href="/article" class="hover:text-cyan-400 transition">文章</a>
                <a href="/order-search" class="hover:text-cyan-400 transition">订单查询</a>
                @auth
                    <div class="relative group">
                        <button class="flex items-center space-x-2 hover:text-cyan-400 transition">
                            <i class="fas fa-user"></i>
                            <span>{{ Auth::user()->email }}</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                            <div class="py-1">
                                <a href="/user" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                                    <i class="fas fa-tachometer-alt mr-2"></i>控制面板
                                </a>
                                <a href="/order-search" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                                    <i class="fas fa-shopping-bag mr-2"></i>订单信息
                                </a>
                                <a href="/user/invite" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                                    <i class="fas fa-gift mr-2"></i>返利信息
                                </a>
                                <div class="border-t border-gray-600"></div>
                                <form method="POST" action="/logout" class="block">
                                    @csrf
                                    <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                                        <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <a href="/login" class="bg-cyan-600 px-4 py-2 rounded hover:bg-cyan-700 transition">登录</a>
                    <a href="/register" class="bg-purple-600 px-4 py-2 rounded hover:bg-purple-700 transition">注册</a>
                @endauth
            </div>
        </div>
    </div>
</nav>

<!-- 移动端菜单 -->
<div class="md:hidden">
    <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-black bg-opacity-90">
        <a href="/" class="block px-3 py-2 text-white hover:text-cyan-400">主页</a>
        <a href="/article" class="block px-3 py-2 text-white hover:text-cyan-400">文章</a>
        <a href="/order-search" class="block px-3 py-2 text-white hover:text-cyan-400">订单查询</a>
        @auth
            <a href="/user" class="block px-3 py-2 text-white hover:text-cyan-400">控制面板</a>
            <form method="POST" action="/logout" class="block">
                @csrf
                <button type="submit" class="block w-full text-left px-3 py-2 text-white hover:text-cyan-400">退出登录</button>
            </form>
        @else
            <a href="/login" class="block px-3 py-2 text-white hover:text-cyan-400">登录</a>
            <a href="/register" class="block px-3 py-2 text-white hover:text-cyan-400">注册</a>
        @endauth
    </div>
</div>


