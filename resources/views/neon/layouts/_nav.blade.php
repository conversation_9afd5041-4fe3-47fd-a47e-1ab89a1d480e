
0<!-- Navigation bar (Page header) -->
<header class="navbar-sticky sticky-top container z-fixed px-2" data-sticky-element>
  <div class="navbar navbar-expand-lg flex-nowrap bg-body rounded-pill shadow ps-0 mx-1">
    <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark rounded-pill z-0 d-none d-block-dark"></div>

    <!-- Mobile offcanvas menu toggler (Hamburger) -->
    <button type="button" class="navbar-toggler ms-3" data-bs-toggle="offcanvas" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>

    <!-- Navbar brand (Logo) -->
    <a class="navbar-brand position-relative z-1 ms-4 ms-sm-5 ms-lg-4 me-2 me-sm-0 me-lg-3" href="/">
      <img
        src="{{ picture_url(dujiaoka_config_get('img_logo')) }}"
        class="d-flex d-none d-md-inline-flex justify-content-center align-items-center flex-shrink-0 me-1 {{ dujiaoka_theme_get('invert_logo', 0) ? 'invert_logo' : '' }}"
        style="width: 2.5rem; height: 2.5rem"
      />
      {{ dujiaoka_config_get('text_logo') }}
    </a>

    <!-- Main navigation that turns into offcanvas on screens < 992px wide (lg breakpoint) -->
    <nav class="offcanvas offcanvas-start" id="navbarNav" tabindex="-1" aria-labelledby="navbarNavLabel">
      <div class="offcanvas-header py-3">
        <h5 class="offcanvas-title" id="navbarNavLabel">导航菜单</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body pt-3 pb-4 py-lg-0 mx-lg-auto">
        <ul class="navbar-nav position-relative">
          <li class="nav-item me-lg-n1 me-xl-0">
            <a class="nav-link fs-sm @if(\Illuminate\Support\Facades\Request::url() == url('/')) active @endif" href="/">主页</a>
          </li>
          <li class="nav-item dropdown me-lg-n1 me-xl-0">
            <a class="nav-link dropdown-toggle fs-sm" role="button" data-bs-toggle="dropdown" data-bs-trigger="hover" data-bs-auto-close="outside" aria-expanded="false">联系客服</a>
            <ul class="dropdown-menu" style="--cz-dropdown-spacer: 1rem">
              <li><a class="dropdown-item" href="https://t.me/riniba" target="_blank">站点客服</a></li>
              <li><a class="dropdown-item" href="https://t.me/riniba" target="_blank">Telegram客服</a></li>
            </ul>
          </li>
          <li class="nav-item me-lg-n2 me-xl-0">
            <a class="nav-link fs-sm" href="#modalId" style="font-size: 0.625rem; letter-spacing: 0.05rem" data-bs-toggle="modal" data-bstarget="#modalId">站点公告</a>
              </li>

                   </nav>

        <!-- Button group -->
        <div class="d-flex gap-sm-1 position-relative z-1">
                           <!-- Theme switcher (light/dark/auto) -->
          <div class="dropdown">
            <button type="button" class="theme-switcher btn btn-icon btn-outline-secondary fs-lg border-0 rounded-circle animate-scale" data-bs-toggle="dropdown" data-bs-display="dynamic" aria-expanded="false" aria-label="Toggle theme (light)">
              <span class="theme-icon-active d-flex animate-target">
                <i class="ci-sun"></i>
              </span>
            </button>
            <ul class="dropdown-menu start-50 translate-middle-x" style="--cz-dropdown-min-width: 9rem; --cz-dropdown-spacer: 1rem">
              <li>
                <button type="button" class="dropdown-item active" data-bs-theme-value="light" aria-pressed="true">
                  <span class="theme-icon d-flex fs-base me-2">
                    <i class="ci-sun"></i>
                  </span>
                  <span class="theme-label">亮色</span>
                  <i class="item-active-indicator ci-check ms-auto"></i>
                </button>
              </li>
              <li>
                <button type="button" class="dropdown-item" data-bs-theme-value="dark" aria-pressed="false">
                  <span class="theme-icon d-flex fs-base me-2">
                    <i class="ci-moon"></i>
                  </span>
                  <span class="theme-label">暗色</span>
                  <i class="item-active-indicator ci-check ms-auto"></i>
                </button>
              </li>
              <li>
                <button type="button" class="dropdown-item" data-bs-theme-value="auto" aria-pressed="false">
                  <span class="theme-icon d-flex fs-base me-2">
                    <i class="ci-auto"></i>
                  </span>
                  <span class="theme-label">自动</span>
                  <i class="item-active-indicator ci-check ms-auto"></i>
                </button>
              </li>
            </ul>
          </div>

      <!-- Search -->
      <a class="btn btn-icon fs-lg btn-outline-secondary border-0 rounded-circle animate-scale me-2" href="/order-search">
        <i class="ci-search animate-target"></i>
      </a>
<!-- PC端导航栏右上角头像按钮及菜单，仅PC端显示（大于等于lg宽度）-->
<div class="d-none d-lg-flex align-items-center ms-auto gap-2">
  <div class="dropdown">
    <a
      href="#"
      class="btn btn-icon border-0 bg-transparent p-0 rounded-circle position-relative"
      id="userMenuDropdown"
      data-bs-toggle="dropdown"
      aria-expanded="false"
      style="width:2.5rem;height:2.5rem;"
    >
      <span class="position-relative d-inline-block" style="width:40px;height:40px;">
        @auth
          @if(Auth::user()->avatar)
            <img src="{{ Auth::user()->avatar }}"
                 alt="头像"
                 class="rounded-circle"
                 style="width:40px;height:40px;object-fit:cover;">
            <!-- 绿色打勾 -->
            <span style="position:absolute;bottom:2px;right:2px;width:16px;height:16px;border-radius:50%;background:#2196f3;border:2px solid #fff;display:flex;align-items:center;justify-content:center;">
              <svg width="11" height="11" viewBox="0 0 1024 1024" fill="#fff">
                <path d="M416 704l-192-192 57.6-57.6L416 588.8l326.4-326.4L800 320z"/>
              </svg>
            </span>
          @else
            <!-- 绿色小人+光影效果 -->
            <div class="shimmer-wrapper" style="width:40px;height:40px;border-radius:50%;overflow:hidden;position:relative;">
              <svg viewBox="0 0 1024 1024" width="40" height="40" fill="#2196f3" style="vertical-align:middle;position:relative;z-index:1;">
                <path d="M512 128a192 192 0 1 1 0 384 192 192 0 0 1 0-384zm0 480c176 0 320 79.058 320 176v48H192v-48c0-96.942 144-176 320-176z" />
              </svg>
              <div class="shimmer"></div>
            </div>
            <span style="position:absolute;bottom:2px;right:2px;width:16px;height:16px;border-radius:50%;background:#2196f3;border:2px solid #fff;display:flex;align-items:center;justify-content:center;">
              <svg width="11" height="11" viewBox="0 0 1024 1024" fill="#fff">
                <path d="M416 704l-192-192 57.6-57.6L416 588.8l326.4-326.4L800 320z"/>
              </svg>
            </span>
          @endif
        @else
          <svg viewBox="0 0 1024 1024" width="40" height="40" fill="#bbb" style="vertical-align:middle;">
            <path d="M512 128a192 192 0 1 1 0 384 192 192 0 0 1 0-384zm0 480c176 0 320 79.058 320 176v48H192v-48c0-96.942 144-176 320-176z" />
          </svg>
        @endauth
      </span>
    </a>
    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuDropdown">
      @auth
        <li><span class="h6 dropdown-header">{{ Auth::user()->email }}</span></li>
        <li><a class="dropdown-item" href="/user"><i class="ci-grid me-2"></i>控制面板</a></li>
        <li><a class="dropdown-item" href="/order-search"><i class="ci-shopping-bag me-2"></i>订单信息</a></li>
        <li><a class="dropdown-item" href="/user/invite"><i class="ci-gift me-2"></i>返利信息</a></li>
        <li><a class="dropdown-item" href="/account"><i class="ci-settings me-2"></i>个人设置</a></li>
        <li><hr></li>
        <li>
          <form method="POST" action="/logout" class="mb-0">
            @csrf
            <button type="submit" class="dropdown-item">
              <i class="ci-log-out me-2"></i>退出登录
            </button>
          </form>
        </li>
      @else
        <li><a class="dropdown-item" href="/login"><i class="ci-log-in me-2"></i>登录</a></li>
        <li><a class="dropdown-item" href="/register"><i class="ci-user-plus me-2"></i>注册</a></li>
      @endauth
    </ul>
  </div>
</div>

<!-- 移动端顶部头像按钮区 -->
<div class="d-flex d-lg-none align-items-center justify-content-end ms-auto px-2">
  <div class="dropdown">
    <a
      href="#"
      class="btn btn-icon border-0 bg-transparent p-0 rounded-circle position-relative"
      id="userMenuMobileDropdown"
      data-bs-toggle="dropdown"
      aria-expanded="false"
      style="width:2.5rem;height:2.5rem;"
    >
      <span class="position-relative d-inline-block" style="width:40px;height:40px;">
        @auth
          @if(Auth::user()->avatar)
            <img src="{{ Auth::user()->avatar }}"
                 alt="头像"
                 class="rounded-circle"
                 style="width:40px;height:40px;object-fit:cover;">
            <span style="position:absolute;bottom:2px;right:2px;width:16px;height:16px;border-radius:50%;background:#2196f3;border:2px solid #fff;display:flex;align-items:center;justify-content:center;">
              <svg width="11" height="11" viewBox="0 0 1024 1024" fill="#fff">
                <path d="M416 704l-192-192 57.6-57.6L416 588.8l326.4-326.4L800 320z"/>
              </svg>
            </span>
          @else
            <!-- 绿色小人+光影效果 -->
            <div class="shimmer-wrapper" style="width:40px;height:40px;border-radius:50%;overflow:hidden;position:relative;">
              <svg viewBox="0 0 1024 1024" width="40" height="40" fill="#2196f3" style="vertical-align:middle;position:relative;z-index:1;">
                <path d="M512 128a192 192 0 1 1 0 384 192 192 0 0 1 0-384zm0 480c176 0 320 79.058 320 176v48H192v-48c0-96.942 144-176 320-176z" />
              </svg>
              <div class="shimmer"></div>
            </div>
            <span style="position:absolute;bottom:2px;right:2px;width:16px;height:16px;border-radius:50%;background:#2196f3;border:2px solid #fff;display:flex;align-items:center;justify-content:center;">
              <svg width="11" height="11" viewBox="0 0 1024 1024" fill="#fff">
                <path d="M416 704l-192-192 57.6-57.6L416 588.8l326.4-326.4L800 320z"/>
              </svg>
            </span>
          @endif
        @else
          <svg viewBox="0 0 1024 1024" width="40" height="40" fill="#bbb" style="vertical-align:middle;">
            <path d="M512 128a192 192 0 1 1 0 384 192 192 0 0 1 0-384zm0 480c176 0 320 79.058 320 176v48H192v-48c0-96.942 144-176 320-176z" />
          </svg>
        @endauth
      </span>
    </a>
    <ul class="dropdown-menu dropdown-menu-end mt-2" aria-labelledby="userMenuMobileDropdown" style="min-width:12rem;">
      @auth
        <li><span class="h6 dropdown-header">余额：{{ number_format(Auth::user()->money/100,2) }} {{ dujiaoka_config_get('global_currency') }}</span></li>
        <li><a class="dropdown-item" href="/user"><i class="ci-grid me-2"></i>控制面板</a></li>
        <li><a class="dropdown-item" href="/order-search"><i class="ci-shopping-bag me-2"></i>订单信息</a></li>
        <li><a class="dropdown-item" href="/user/invite"><i class="ci-gift me-2"></i>返利信息</a></li>
        <li><a class="dropdown-item" href="/account"><i class="ci-settings me-2"></i>个人设置</a></li>
        <li><hr></li>
        <li>
          <form method="POST" action="/logout" class="mb-0">
            @csrf
            <button type="submit" class="dropdown-item">
              <i class="ci-log-out me-2"></i>退出登录
            </button>
          </form>
        </li>
      @else
        <li><a class="dropdown-item" href="/login"><i class="ci-log-in me-2"></i>登录</a></li>
        <li><a class="dropdown-item" href="/register"><i class="ci-user-plus me-2"></i>注册</a></li>
      @endauth
    </ul>
  </div>
</div>

<!-- 添加光影滑动CSS -->
<style>
.shimmer-wrapper {
  position: relative;
}

.shimmer {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
/* 呼吸灯效果 */
.shimmer-wrapper svg {
  animation: breathe 2s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* 旋转效果 */
.shimmer-wrapper:hover svg {
  transform: rotate(360deg);
  transition: transform 0.5s ease;
}

/* 可选：鼠标悬停时加速光影效果 */
.shimmer-wrapper:hover .shimmer {
  animation-duration: 1s;
}
</style>

    </div>
  </div>
</header>

    <!-- 置顶按钮 -->
    <div class="floating-buttons position-fixed top-50 end-0 z-sticky me-3 me-xl-4 pb-4">
  <a class="btn-scroll-top btn btn-sm bg-body border-0 rounded-pill shadow animate-slide-end" href="#top">
    <span style="display: inline-block; transform: rotate(90deg)">顶</span>
    <span style="display: inline-block; transform: rotate(90deg)">置</span>
    <i class="ci-arrow-right fs-base ms-1 me-n1 animate-target"></i>
    <span class="position-absolute top-0 start-0 w-100 h-100 border rounded-pill z-0"></span>
    <svg class="position-absolute top-0 start-0 w-100 h-100 z-1" viewBox="0 0 62 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x=".75" y=".75" width="60.5" height="30.5" rx="15.25" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" style="stroke-dasharray: 155.201; stroke-dashoffset: 0"></rect>
    </svg>
  </a>
</div>
<!-- 公告 -->
<div class="modal fade" id="modalId" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg modal-dialog-scrollable" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">公告</h5>
        <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body bg-body-tertiary fs-sm">
        <div class="py-2 px-2 px-md-2">
        <h4 class="">{{ __('dujiaoka.site_announcement') }}：</h4>
                                
        <p class="lead">{!! dujiaoka_config_get('notice') !!}</p>
        </div>
      </div>
      <div class="modal-footer flex-column flex-sm-row align-items-stretch">
        <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">关闭</button>
        <button class="btn btn-dark" type="button" data-bs-dismiss="modal">确定</button>
      </div>
    </div>
  </div>
</div>