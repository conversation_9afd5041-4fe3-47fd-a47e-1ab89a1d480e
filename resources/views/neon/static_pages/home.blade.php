@extends('neon.layouts.default')

@section('content')

<!-- 史诗级呼吸灯动画CSS -->
<style>
/* 定义呼吸灯关键帧动画 - 更强烈的效果 */
@keyframes epic-breathing-glow {
  0% {
    box-shadow: 0 0 5px 0px rgba(0, 123, 255, 0.7);
    transform: scale(0.98);
  }
  50% {
    box-shadow: 0 0 20px 10px rgba(0, 123, 255, 0.5);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 0 5px 0px rgba(0, 123, 255, 0.7);
    transform: scale(0.98);
  }
}

/* 直接针对您的实际HTML结构进行修复 */
.product-card .ratio {
  position: relative;
  overflow: visible !important;
  z-index: 1;
}

/* 关键修复：直接针对实际HTML中的图片结构 */
.product-card .ratio img {
  border-radius: 8px;
  animation: epic-breathing-glow 3s infinite;
  transition: all 0.3s ease;
  z-index: 2;
}

/* 分类图标呼吸灯 */
.breathing-img-container {
  position: relative;
  width: 25px;
  height: 25px;
  margin-right: 8px;
  border-radius: 50%;
  overflow: visible;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.breathing-img {
  width: 25px;
  height: 25px;
  object-fit: cover;
  border-radius: 50%;
  z-index: 1;
}
.breathing-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  animation: epic-breathing-glow 2s infinite;
  z-index: 0;
}

/* 置顶按钮呼吸灯 */
.btn-breathing {
  animation: epic-breathing-glow 2s infinite;
  transition: all 0.3s;
}
</style>
<main class="content-wrapper">
    <section class="container pt-4">
        <div class="row">
            <div class="w-100">
                <div class="position-relative">
                    <span class="position-absolute top-0 start-0 w-100 h-100 rounded-5 d-none-dark rtl-flip"
                        style="background: linear-gradient(90deg, #accbee 0%, #e7f0fd 100%)"></span>
                    <span class="position-absolute top-0 start-0 w-100 h-100 rounded-5 d-none d-block-dark rtl-flip"
                        style="background: linear-gradient(90deg, #1b273a 0%, #1f2632 100%)"></span>
                    <div class="row justify-content-center position-relative z-2">
                        <div class="col-xl-5 col-xxl-5  d-flex align-items-center mt-xl-n3">

                            <!-- Text content master slider -->
                            <div class="swiper px-5 pe-xl-0 ps-xxl-0 me-xl-n5" data-swiper='{
                    "spaceBetween": 64,
                    "loop": true,
                    "speed": 400,
                    "controlSlider": "#sliderImages",
                    "autoplay": {
                      "delay": 5500,
                      "disableOnInteraction": false
                    },
                    "scrollbar": {
                      "el": ".swiper-scrollbar"
                    }
                  }'>
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide text-center text-xl-start pt-5 py-xl-5">
                                        <p class="text-body">近期热销产品</p>
                                        <h2 class="display-4 pb-2 pb-xl-4">美区礼品卡50美刀</h2>
                                        <a class="btn btn-lg btn-dark" href="buy/1">
                                            立即 购买
                                            <i class="ci-arrow-up-right fs-lg ms-2 me-n1"></i>
                                        </a>
                                    </div>
                                    <div class="swiper-slide text-center text-xl-start pt-5 py-xl-5">
                                        <p class="text-body">Telegram社区数万人社区</p>
                                        <h2 class="display-4 pb-2 pb-xl-4">专业售后服务</h2>
                                        <a class="btn btn-lg btn-dark" href="hhttps://t.me/RinibaGroup">
                                            加入 社区
                                            <i class="ci-arrow-up-right fs-lg ms-2 me-n1"></i>
                                        </a>
                                    </div>
                                    <div class="swiper-slide text-center text-xl-start pt-5 py-xl-5">
                                        <p class="text-body">发货时间少于两分钟</p>
                                        <h2 class="display-4 pb-2 pb-xl-4">及时交货</h2>
                                        <a class="btn btn-lg btn-dark rounded-pill"
                                            href="https://youtu.be/me_Dc5PJrXk?si=QSILRHdbjUIJ7SmV" data-glightbox
                                            data-gallery="video">
                                            <i class="ci-play fs-lg ms-n1 me-2"></i>
                                            播放 视频
                                        </a>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="col-9 col-sm-7 col-md-6 col-lg-5 col-xl-5">
                            <!-- Binded images (controlled slider) -->
                            <div class="swiper user-select-none" id="sliderImages" data-swiper='{
                    "allowTouchMove": false,
                    "loop": true,
                    "effect": "fade",
                    "fadeEffect": {
                      "crossFade": true
                    }
                  }'>
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide d-flex justify-content-end">
                                        <div class="ratio rtl-flip"
                                            style="max-width: 400px; --cz-aspect-ratio: calc(537 / 495 * 100%)">
                                            <img src="/assets/riniba_03/banner/1.webp" alt="Image">
                                        </div>
                                    </div>
                                    <div class="swiper-slide d-flex justify-content-end">
                                        <div class="ratio rtl-flip"
                                            style="max-width: 400px; --cz-aspect-ratio: calc(537 / 495 * 100%)">
                                            <img src="/assets/riniba_03/banner/2.webp" alt="Image">
                                        </div>
                                    </div>
                                    <div class="swiper-slide d-flex justify-content-end">
                                        <div class="ratio rtl-flip"
                                            style="max-width: 400px; --cz-aspect-ratio: calc(537 / 495 * 100%)">
                                            <img src="/assets/riniba_03/banner/3.webp" alt="Image">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Scrollbar -->
                    <div class="row justify-content-center" data-bs-theme="dark">
                        <div class="col-xxl-10">
                            <div class="position-relative mx-5 mx-xxl-0">
                                <div class="swiper-scrollbar mb-4"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<!-- 分类与商品区 -->
<section class="container pt-3 mt-2 mt-sm-3 mt-lg-4 mt-xl-2">
    <div class="row g-0 overflow-x-auto pb-2 pb-sm-3 mb-3">
        <div class="col-auto pb-1 pb-sm-0 mx-auto">
            <ul class="nav nav-pills justify-content-center">
                <!-- 全部分类按钮 -->
                <li class="nav-item">
                    <a href="#group-all" data-bs-toggle="pill" class="btn btn-outline-secondary d-flex align-items-center active" style="min-width: 10px;">
                        <svg width="25" height="25" viewBox="0 0 24 24" fill="none" style="margin-right:8px;">
                            <path d="M6 6h15l-1.5 9h-13l-1-5H4" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="9" cy="25" r="1" fill="#000"/>
                            <circle cx="18" cy="25" r="1" fill="#000"/>
                        </svg>
                        全部
                    </a>
                </li>
                @foreach($data as $group)
                <li class="nav-item">
                    <a href="#group-{{ $group['id'] }}" data-bs-toggle="pill" class="btn btn-outline-secondary d-flex align-items-center" style="min-width: 100px;">
                        @php
                            $imgSrc = category_picture_url($group['picture'] ?? '');
                        @endphp
                        <div class="breathing-img-container">
                            <div class="breathing-glow"></div>
                            <img src="{{ $imgSrc }}" class="breathing-img">
                        </div>
                        <span>{{ $group['gp_name'] }}</span>
                    </a>
                </li>
                @endforeach
            </ul>
        </div>
    </div>
</section>

<!-- 搜索框 -->
<div class="d-flex justify-content-center mb-4">
    <div class="position-relative col-12 col-md-6">
        <i class="ci-search position-absolute top-50 start-0 translate-middle-y ms-3"></i>
        <input type="search" class="quicksearch form-control form-icon-start" placeholder="搜索您的商品..." id="searchInput">
        <button class="btn btn-sm btn-outline-secondary w-auto border-0 p-1 position-absolute top-50 end-0 translate-middle-y me-2 clear-btn" id="clearBtn">
            <svg class="opacity-75" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.619 5.381a.875.875 0 0 1 0 1.238l-12 12a.875.875 0 0 1-1.238-1.238l12-12a.875.875 0 0 1 1.238 0Z"></path>
                <path d="M5.381 5.381a.875.875 0 0 1 1.238 0l12 12a.875.875 0 1 1-1.238 1.238l-12-12a.875.875 0 0 1 0-1.238Z"></path>
            </svg>
        </button>
    </div>
</div>

<!-- 置顶按钮，带呼吸灯 -->
<div class="floating-buttons position-fixed top-50 end-0 z-sticky me-3 me-xl-4 pb-4">
    <a class="btn-scroll-top btn btn-sm bg-body border-0 rounded-pill shadow animate-slide-end btn-breathing" href="#top">
        <span style="display: inline-block; transform: rotate(90deg);">顶</span>
        <span style="display: inline-block; transform: rotate(90deg);">置</span>
        <i class="ci-arrow-right fs-base ms-1 me-n1 animate-target"></i>
        <span class="position-absolute top-0 start-0 w-100 h-100 border rounded-pill z-0"></span>
        <svg class="position-absolute top-0 start-0 w-100 h-100 z-1" viewBox="0 0 62 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x=".75" y=".75" width="60.5" height="30.5" rx="15.25" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" style="stroke-dasharray: 155.201; stroke-dashoffset: 0;"></rect>
        </svg>
    </a>
</div>

<!-- 商品展示区 -->
<div class="container pt-4" id="goodsContainer">
    <div class="tab-content">
        <!-- 全部商品 -->
        <div class="tab-pane fade show active" id="group-all">
            <div class="row row-cols-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 row-cols-xxl-6 g-4 pt-4 goods-list">
                @foreach($data as $group)
                    @foreach($group['goods'] as $goods)
                        @include('neon.layouts._goods', ['goods' => $goods, 'group' => $group])
                    @endforeach
                @endforeach
            </div>
        </div>
        <!-- 分类商品 -->
        @foreach($data as $group)
        <div class="tab-pane fade" id="group-{{ $group['id'] }}">
            <div class="row row-cols-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 row-cols-xxl-6 g-4 pt-4 goods-list">
                @foreach($group['goods'] as $goods)
                    @include('neon.layouts._goods', ['goods' => $goods, 'group' => $group])
                @endforeach
            </div>
        </div>
        @endforeach
    </div>
</div>
@endsection

@section('scripts')
<script>

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 搜索功能
    var searchInput = document.getElementById('searchInput');
    var clearBtn = document.getElementById('clearBtn');
    var tabs = document.querySelectorAll('.tab-pane');
    searchInput.addEventListener('input', function(){
        var val = searchInput.value.toLowerCase().trim();
        tabs.forEach(function(tab) {
            tab.querySelectorAll('.goods-item').forEach(function(card) {
                var goodsName = card.getAttribute("data-name").toLowerCase();
                var groupName = card.getAttribute("data-group").toLowerCase();
                if(goodsName.indexOf(val) !== -1 || groupName.indexOf(val) !== -1){
                    card.style.display = "";
                }else{
                    card.style.display = "none";
                }
            });
        });
    });
    clearBtn.addEventListener('click',function(){
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    });
    clearBtn.addEventListener('click',function(){
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    });
    
    // 强制应用呼吸灯效果到所有商品图片
    // 这是确保效果生效的关键部分
    setTimeout(function() {
        document.querySelectorAll('.goods-item img').forEach(function(img) {
            img.style.animation = 'epic-breathing-glow 3s infinite';
            img.style.borderRadius = '8px';
        });
    }, 100);
});
    // 史诗级修复：强制应用呼吸灯效果到所有商品图片
    // 这是确保效果生效的终极方案
    setTimeout(function() {
        // 针对您提供的实际HTML结构的选择器
        document.querySelectorAll('.product-card .ratio img').forEach(function(img) {
            img.style.animation = 'epic-breathing-glow 3s infinite';
            img.style.borderRadius = '8px';
            // 确保父容器设置为overflow:visible
            var ratioDiv = img.closest('.ratio');
            if(ratioDiv) {
                ratioDiv.style.overflow = 'visible';
            }
            // 确保祖父容器也设置为overflow:visible
            var positionDiv = ratioDiv ? ratioDiv.parentElement : null;
            if(positionDiv) {
                positionDiv.style.overflow = 'visible';
            }
        });
    }, 100);
});
</script>
@endsection
</script>
@endsection