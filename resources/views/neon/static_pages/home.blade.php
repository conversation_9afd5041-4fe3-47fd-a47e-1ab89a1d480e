@extends('neon.layouts.default')

@section('content')
<!-- 首页 - 现代化设计 -->

<!-- 英雄区域 -->
<section class="hero-section relative min-h-screen flex items-center justify-center overflow-hidden" data-aos="fade-up">
    <!-- 动态背景 -->
    <div class="absolute inset-0 z-0">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 opacity-80"></div>
        <div id="particles-js" class="absolute inset-0"></div>

        <!-- 浮动元素 -->
        <div class="floating-elements">
            <div class="floating-element absolute top-20 left-10 w-20 h-20 bg-blue-500 rounded-full opacity-20 animate-bounce"></div>
            <div class="floating-element absolute top-40 right-20 w-16 h-16 bg-purple-500 rounded-full opacity-30 animate-pulse"></div>
            <div class="floating-element absolute bottom-20 left-20 w-24 h-24 bg-pink-500 rounded-full opacity-25 animate-bounce" style="animation-delay: 1s;"></div>
            <div class="floating-element absolute bottom-40 right-10 w-12 h-12 bg-green-500 rounded-full opacity-20 animate-pulse" style="animation-delay: 2s;"></div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- 左侧内容 -->
            <div class="text-center lg:text-left" data-aos="fade-right" data-aos-delay="200">
                <div class="mb-6">
                    <span class="inline-block px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-full mb-4">
                        🚀 专业数字商品交易平台
                    </span>
                </div>

                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                    <span class="gradient-text">{{ dujiaoka_config_get('title', '自动发卡平台') }}</span>
                    <br>
                    <span class="text-white">安全 · 快速 · 可靠</span>
                </h1>

                <p class="text-xl text-gray-300 mb-8 leading-relaxed">
                    {{ dujiaoka_config_get('description', '提供全球数字商品自动发卡服务，支持多种支付方式，7x24小时自动发货，让您的购物体验更加便捷安全。') }}
                </p>

                <!-- 特色数据 -->
                <div class="grid grid-cols-3 gap-6 mb-8">
                    <div class="text-center" data-aos="fade-up" data-aos-delay="400">
                        <div class="text-3xl font-bold gradient-text" data-count="50000">0</div>
                        <div class="text-sm text-gray-400">用户信赖</div>
                    </div>
                    <div class="text-center" data-aos="fade-up" data-aos-delay="500">
                        <div class="text-3xl font-bold gradient-text" data-count="99.9">0</div>
                        <div class="text-sm text-gray-400">成功率 %</div>
                    </div>
                    <div class="text-center" data-aos="fade-up" data-aos-delay="600">
                        <div class="text-3xl font-bold gradient-text" data-count="24">0</div>
                        <div class="text-sm text-gray-400">小时服务</div>
                    </div>
                </div>

                <!-- 行动按钮 -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="#products" class="btn-primary inline-flex items-center justify-center px-8 py-4 text-lg font-semibold">
                        <i class="fas fa-shopping-bag mr-2"></i>
                        立即购买
                    </a>
                    <a href="{{ url('/article') }}" class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white border-opacity-30 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all duration-300">
                        <i class="fas fa-question-circle mr-2"></i>
                        了解更多
                    </a>
                </div>

                <!-- 支付方式 -->
                <div class="mt-12" data-aos="fade-up" data-aos-delay="700">
                    <p class="text-sm text-gray-400 mb-4">支持多种支付方式</p>
                    <div class="flex flex-wrap gap-4 justify-center lg:justify-start">
                        <div class="payment-icon tooltip" data-tooltip="支付宝">
                            <i class="fab fa-alipay text-blue-500 text-2xl hover-scale"></i>
                        </div>
                        <div class="payment-icon tooltip" data-tooltip="微信支付">
                            <i class="fab fa-weixin text-green-500 text-2xl hover-scale"></i>
                        </div>
                        <div class="payment-icon tooltip" data-tooltip="比特币">
                            <i class="fab fa-bitcoin text-yellow-500 text-2xl hover-scale"></i>
                        </div>
                        <div class="payment-icon tooltip" data-tooltip="PayPal">
                            <i class="fab fa-paypal text-blue-600 text-2xl hover-scale"></i>
                        </div>
                        <div class="payment-icon tooltip" data-tooltip="银行卡">
                            <i class="fas fa-credit-card text-purple-500 text-2xl hover-scale"></i>
                        </div>
                        <div class="payment-icon tooltip" data-tooltip="USDT">
                            <i class="fab fa-ethereum text-indigo-500 text-2xl hover-scale"></i>
                        </div>
                    </div>
                </div>

                <!-- 实时状态指示器 -->
                <div class="mt-8" data-aos="fade-up" data-aos-delay="800">
                    <div class="flex items-center justify-center lg:justify-start space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="status-indicator online"></div>
                            <span class="text-sm text-gray-400">系统运行正常</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="status-indicator online"></div>
                            <span class="text-sm text-gray-400">自动发货中</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="status-indicator online"></div>
                            <span class="text-sm text-gray-400">客服在线</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧图片/动画 -->
            <div class="relative" data-aos="fade-left" data-aos-delay="300">
                <div class="relative z-10">
                    <!-- 主要展示卡片 -->
                    <div class="card-modern p-8 text-center transform rotate-3 hover:rotate-0 transition-transform duration-500">
                        <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 glow-effect">
                            <i class="fas fa-bolt text-white text-3xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-4">极速发货</h3>
                        <p class="text-gray-300 mb-6">自动化系统确保订单在2分钟内完成处理</p>
                        <div class="flex justify-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                        </div>
                    </div>

                    <!-- 浮动卡片 -->
                    <div class="absolute -top-6 -left-6 card-modern p-4 w-48 transform -rotate-12 hover:rotate-0 transition-transform duration-500" data-aos="fade-up" data-aos-delay="800">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-shield-alt text-white"></i>
                            </div>
                            <div>
                                <div class="text-white font-semibold">安全保障</div>
                                <div class="text-sm text-gray-400">SSL加密</div>
                            </div>
                        </div>
                    </div>

                    <div class="absolute -bottom-6 -right-6 card-modern p-4 w-48 transform rotate-12 hover:rotate-0 transition-transform duration-500" data-aos="fade-up" data-aos-delay="900">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-headset text-white"></i>
                            </div>
                            <div>
                                <div class="text-white font-semibold">24/7客服</div>
                                <div class="text-sm text-gray-400">全天候支持</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 背景装饰 -->
                <div class="absolute inset-0 -z-10">
                    <div class="absolute top-10 left-10 w-32 h-32 bg-blue-500 rounded-full blur-3xl opacity-30"></div>
                    <div class="absolute bottom-10 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl opacity-30"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 滚动指示器 -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <a href="#features" class="flex flex-col items-center">
            <span class="text-sm mb-2">向下滚动</span>
            <i class="fas fa-chevron-down"></i>
        </a>
    </div>
</section>

<!-- 特色功能区域 -->
<section id="features" class="py-20 relative" data-aos="fade-up">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold gradient-text mb-4">为什么选择我们</h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                我们致力于为用户提供最优质的数字商品购买体验，以下是我们的核心优势
            </p>
        </div>

        <!-- 特色网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 特色1 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="100">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-bolt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">极速发货</h3>
                    <p class="text-gray-400 leading-relaxed">
                        采用全自动化系统，订单确认后2分钟内自动发货，无需等待人工处理，让您第一时间获得商品。
                    </p>
                    <div class="mt-6 flex justify-center">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 特色2 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="200">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-shield-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">安全保障</h3>
                    <p class="text-gray-400 leading-relaxed">
                        采用银行级SSL加密技术，保护您的个人信息和交易数据安全，所有商品均来源正规渠道。
                    </p>
                    <div class="mt-6">
                        <div class="flex justify-center items-center space-x-2">
                            <i class="fas fa-lock text-green-500"></i>
                            <span class="text-sm text-green-500 font-medium">SSL加密保护</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 特色3 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="300">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-headset text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">24/7客服</h3>
                    <p class="text-gray-400 leading-relaxed">
                        专业客服团队全天候在线，通过QQ、微信、Telegram等多种方式为您提供及时的售前售后服务。
                    </p>
                    <div class="mt-6 flex justify-center space-x-3">
                        <i class="fab fa-qq text-blue-500 text-lg"></i>
                        <i class="fab fa-weixin text-green-500 text-lg"></i>
                        <i class="fab fa-telegram text-blue-400 text-lg"></i>
                    </div>
                </div>
            </div>

            <!-- 特色4 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="400">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-credit-card text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">多种支付</h3>
                    <p class="text-gray-400 leading-relaxed">
                        支持支付宝、微信、银行卡、数字货币等多种支付方式，满足不同用户的支付需求。
                    </p>
                    <div class="mt-6 flex justify-center space-x-3">
                        <i class="fab fa-alipay text-blue-500 text-lg"></i>
                        <i class="fab fa-weixin text-green-500 text-lg"></i>
                        <i class="fab fa-bitcoin text-yellow-500 text-lg"></i>
                    </div>
                </div>
            </div>

            <!-- 特色5 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="500">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-undo text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">售后保障</h3>
                    <p class="text-gray-400 leading-relaxed">
                        提供完善的售后服务，如遇商品问题支持退换，让您购买无忧，使用放心。
                    </p>
                    <div class="mt-6">
                        <div class="flex justify-center items-center space-x-2">
                            <i class="fas fa-check-circle text-green-500"></i>
                            <span class="text-sm text-green-500 font-medium">品质保证</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 特色6 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="600">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">用户信赖</h3>
                    <p class="text-gray-400 leading-relaxed">
                        已服务超过50,000名用户，获得99.9%的好评率，是您值得信赖的数字商品购买平台。
                    </p>
                    <div class="mt-6">
                        <div class="flex justify-center items-center space-x-2">
                            <div class="flex text-yellow-500">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-sm text-gray-400">5.0分</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 热门推荐区域 -->
<section class="py-20 relative" data-aos="fade-up">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold gradient-text mb-4">热门推荐</h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                精选优质数字商品，为您提供最佳购买体验
            </p>
        </div>

        <!-- 推荐商品轮播 -->
        <div class="relative">
            <div class="swiper-container" id="featuredProducts">
                <div class="swiper-wrapper">
                    <!-- 推荐商品1 -->
                    <div class="swiper-slide">
                        <div class="card-modern p-8 text-center product-card">
                            <div class="relative mb-6">
                                <img src="/assets/images/products/steam-card.jpg" alt="Steam礼品卡" class="w-32 h-32 mx-auto rounded-xl object-cover">
                                <div class="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                                    热销
                                </div>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-2">Steam礼品卡</h3>
                            <p class="text-gray-400 mb-4">全球通用，即时到账</p>
                            <div class="text-2xl font-bold gradient-text mb-4">¥50 - ¥500</div>
                            <a href="{{ url('/buy/1') }}" class="btn-primary w-full">
                                <i class="fas fa-shopping-cart mr-2"></i>
                                立即购买
                            </a>
                        </div>
                    </div>

                    <!-- 推荐商品2 -->
                    <div class="swiper-slide">
                        <div class="card-modern p-8 text-center product-card">
                            <div class="relative mb-6">
                                <img src="/assets/images/products/itunes-card.jpg" alt="iTunes礼品卡" class="w-32 h-32 mx-auto rounded-xl object-cover">
                                <div class="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                                    新品
                                </div>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-2">iTunes礼品卡</h3>
                            <p class="text-gray-400 mb-4">苹果生态专用</p>
                            <div class="text-2xl font-bold gradient-text mb-4">¥100 - ¥1000</div>
                            <a href="{{ url('/buy/2') }}" class="btn-primary w-full">
                                <i class="fas fa-shopping-cart mr-2"></i>
                                立即购买
                            </a>
                        </div>
                    </div>

                    <!-- 推荐商品3 -->
                    <div class="swiper-slide">
                        <div class="card-modern p-8 text-center product-card">
                            <div class="relative mb-6">
                                <img src="/assets/images/products/google-play.jpg" alt="Google Play礼品卡" class="w-32 h-32 mx-auto rounded-xl object-cover">
                                <div class="absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                                    推荐
                                </div>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-2">Google Play礼品卡</h3>
                            <p class="text-gray-400 mb-4">安卓应用商店</p>
                            <div class="text-2xl font-bold gradient-text mb-4">¥50 - ¥500</div>
                            <a href="{{ url('/buy/3') }}" class="btn-primary w-full">
                                <i class="fas fa-shopping-cart mr-2"></i>
                                立即购买
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 导航按钮 -->
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>

                <!-- 分页器 -->
                <div class="swiper-pagination"></div>
            </div>
        </div>

        <!-- 查看更多按钮 -->
        <div class="text-center mt-12">
            <a href="#products" class="btn-secondary px-8 py-4 text-lg">
                <i class="fas fa-th-large mr-2"></i>
                查看全部商品
            </a>
        </div>
    </div>
</section>
<!-- 商品分类区域 -->
<section id="products" class="py-20 relative" data-aos="fade-up">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold gradient-text mb-4">商品分类</h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                丰富的数字商品种类，满足您的各种需求
            </p>
        </div>

        <!-- 搜索框 -->
        <div class="max-w-2xl mx-auto mb-12">
            <div class="relative">
                <input type="search"
                       class="w-full pl-12 pr-4 py-4 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                       placeholder="搜索您需要的商品..."
                       id="searchInput">
                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400 text-lg"></i>
                </div>
                <button class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-white transition-colors">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </div>

        <!-- 分类标签 -->
        <div class="flex flex-wrap justify-center gap-4 mb-12">
            <button class="category-tab active" data-category="all">
                <i class="fas fa-th-large mr-2"></i>
                全部商品
            </button>
            @foreach($data as $group)
            <button class="category-tab" data-category="{{ $group['id'] }}">
                @if($group['picture'])
                    <img src="{{ category_picture_url($group['picture']) }}" alt="{{ $group['gp_name'] }}" class="w-6 h-6 mr-2 rounded">
                @else
                    <i class="fas fa-tag mr-2"></i>
                @endif
                {{ $group['gp_name'] }}
            </button>
            @endforeach
        </div>

        <!-- 商品展示区域 -->
        <div class="products-container" id="goodsContainer">
            <!-- 全部商品 -->
            <div class="products-grid active" id="category-all">
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6">
                    @foreach($data as $group)
                        @foreach($group['goods'] as $goods)
                            <div class="product-item" data-category="{{ $group['id'] }}" data-name="{{ $goods['gd_name'] }}" data-group="{{ $group['gp_name'] }}">
                                @include('neon.layouts._goods_modern', ['goods' => $goods, 'group' => $group])
                            </div>
                        @endforeach
                    @endforeach
                </div>
            </div>

            <!-- 分类商品 -->
            @foreach($data as $group)
            <div class="products-grid" id="category-{{ $group['id'] }}">
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6">
                    @foreach($group['goods'] as $goods)
                        <div class="product-item" data-name="{{ $goods['gd_name'] }}" data-group="{{ $group['gp_name'] }}">
                            @include('neon.layouts._goods_modern', ['goods' => $goods, 'group' => $group])
                        </div>
                    @endforeach
                </div>
            </div>
            @endforeach
        </div>

        <!-- 加载更多按钮 -->
        <div class="text-center mt-12">
            <button class="btn-secondary px-8 py-4 text-lg" id="loadMoreBtn">
                <i class="fas fa-plus mr-2"></i>
                加载更多商品
            </button>
        </div>
    </div>
</section>

<!-- 客户评价区域 -->
<section class="py-20 relative" data-aos="fade-up">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold gradient-text mb-4">客户评价</h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                听听我们客户的真实反馈
            </p>
        </div>

        <!-- 评价网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 评价1 -->
            <div class="card-modern p-6" data-aos="fade-up" data-aos-delay="100">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                        <div class="font-bold text-white">张先生</div>
                        <div class="flex text-yellow-500 text-sm">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
                <p class="text-gray-300">
                    "发货速度非常快，2分钟就收到了Steam礼品卡，客服态度也很好，下次还会再来购买。"
                </p>
            </div>

            <!-- 评价2 -->
            <div class="card-modern p-6" data-aos="fade-up" data-aos-delay="200">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                        <div class="font-bold text-white">李女士</div>
                        <div class="flex text-yellow-500 text-sm">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
                <p class="text-gray-300">
                    "价格实惠，商品质量很好，已经购买多次了，每次都很满意，强烈推荐给大家。"
                </p>
            </div>

            <!-- 评价3 -->
            <div class="card-modern p-6" data-aos="fade-up" data-aos-delay="300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                        <div class="font-bold text-white">王先生</div>
                        <div class="flex text-yellow-500 text-sm">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
                <p class="text-gray-300">
                    "网站界面很漂亮，操作简单，支付方式多样，是一个值得信赖的购买平台。"
                </p>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化AOS动画
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });

    // 创建星空背景
    createStarField();

    // 初始化粒子效果
    initParticles();

    // 数字动画
    animateNumbers();

    // 搜索功能
    initSearch();

    // 分类切换
    initCategoryTabs();

    // 轮播图初始化
    initSwiper();

    // 返回顶部按钮
    initBackToTop();

    // 实时统计更新
    updateStats();
});

// 创建星空背景
function createStarField() {
    const starsContainer = document.createElement('div');
    starsContainer.className = 'stars-container';
    document.body.appendChild(starsContainer);

    for (let i = 0; i < 100; i++) {
        const star = document.createElement('div');
        star.className = 'star';
        star.style.left = Math.random() * 100 + '%';
        star.style.top = Math.random() * 100 + '%';
        star.style.animationDelay = Math.random() * 3 + 's';
        starsContainer.appendChild(star);
    }
}

// 初始化粒子效果
function initParticles() {
    const heroParticles = document.getElementById('heroParticles');
    if (heroParticles) {
        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.style.position = 'absolute';
            particle.style.width = '2px';
            particle.style.height = '2px';
            particle.style.background = 'rgba(255, 255, 255, 0.5)';
            particle.style.borderRadius = '50%';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.animation = `float ${3 + Math.random() * 4}s ease-in-out infinite`;
            particle.style.animationDelay = Math.random() * 2 + 's';
            heroParticles.appendChild(particle);
        }
    }
}

// 数字动画
function animateNumbers() {
    const counters = document.querySelectorAll('#totalUsers, #totalOrders');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/,/g, ''));
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current).toLocaleString();
        }, 20);
    });
}

// 搜索功能
function initSearch() {
    const searchInput = document.getElementById('searchInput');
    const productItems = document.querySelectorAll('.product-item');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();

            productItems.forEach(item => {
                const name = item.getAttribute('data-name').toLowerCase();
                const group = item.getAttribute('data-group').toLowerCase();

                if (name.includes(searchTerm) || group.includes(searchTerm)) {
                    item.style.display = '';
                    item.style.animation = 'fadeIn 0.3s ease-in-out';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
}

// 分类切换
function initCategoryTabs() {
    const categoryTabs = document.querySelectorAll('.category-tab');
    const productGrids = document.querySelectorAll('.products-grid');

    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const category = this.getAttribute('data-category');

            // 更新活跃标签
            categoryTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // 显示对应分类
            productGrids.forEach(grid => {
                grid.classList.remove('active');
            });

            const targetGrid = document.getElementById(`category-${category}`);
            if (targetGrid) {
                targetGrid.classList.add('active');
                targetGrid.style.animation = 'fadeIn 0.5s ease-in-out';
            }
        });
    });
}

// 轮播图初始化
function initSwiper() {
    if (typeof Swiper !== 'undefined') {
        new Swiper('#featuredProducts', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                640: {
                    slidesPerView: 2,
                },
                768: {
                    slidesPerView: 3,
                },
                1024: {
                    slidesPerView: 4,
                },
            },
        });
    }
}

// 返回顶部按钮
function initBackToTop() {
    const backToTopBtn = document.createElement('button');
    backToTopBtn.className = 'back-to-top';
    backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    document.body.appendChild(backToTopBtn);

    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    });

    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// 实时统计更新
function updateStats() {
    setInterval(() => {
        const userCount = document.getElementById('totalUsers');
        const orderCount = document.getElementById('totalOrders');

        if (userCount) {
            const current = parseInt(userCount.textContent.replace(/,/g, ''));
            userCount.textContent = (current + Math.floor(Math.random() * 3)).toLocaleString();
        }

        if (orderCount) {
            const current = parseInt(orderCount.textContent.replace(/,/g, ''));
            orderCount.textContent = (current + Math.floor(Math.random() * 5)).toLocaleString();
        }
    }, 30000); // 每30秒更新一次
}

// 通知系统
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type} show`;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 页面加载进度条
function showProgressBar() {
    const progressBar = document.createElement('div');
    progressBar.className = 'progress-bar';
    progressBar.style.width = '0%';
    document.body.appendChild(progressBar);

    let width = 0;
    const interval = setInterval(() => {
        width += Math.random() * 10;
        if (width >= 100) {
            width = 100;
            clearInterval(interval);
            setTimeout(() => {
                progressBar.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(progressBar);
                }, 300);
            }, 500);
        }
        progressBar.style.width = width + '%';
    }, 100);
}

// 页面加载时显示进度条
showProgressBar();
</script>

<style>
/* 分类标签样式 */
.category-tab {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-weight: 500;
}

.category-tab:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.category-tab.active {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border-color: transparent;
}

/* 商品网格样式 */
.products-container {
    position: relative;
}

.products-grid {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.products-grid.active {
    display: block;
    opacity: 1;
}

/* Swiper样式覆盖 */
.swiper-button-next,
.swiper-button-prev {
    color: var(--primary-color);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: 50%;
    width: 50px;
    height: 50px;
}

.swiper-pagination-bullet {
    background: var(--primary-color);
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background: var(--accent-color);
}
</style>
@endsection